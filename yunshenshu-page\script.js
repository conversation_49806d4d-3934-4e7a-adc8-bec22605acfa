// ===== GLOBAL VARIABLES =====
let currentTheme = 'light';
let starsCanvas = null;
let starsCtx = null;
let stars = [];
let animationId = null;

// ===== UTILITY FUNCTIONS =====
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }
};

const lerp = (start, end, factor) => {
  return start + (end - start) * factor;
};

// ===== STARS ANIMATION =====
class StarsAnimation {
  constructor(canvas) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.stars = [];
    this.mouse = { x: 0, y: 0 };
    
    this.init();
  }
  
  init() {
    this.resize();
    this.createStars();
    
    window.addEventListener('resize', () => this.resize());
    document.addEventListener('mousemove', (e) => {
      this.mouse.x = e.clientX;
      this.mouse.y = e.clientY;
    });
    
    this.animate();
  }
  
  resize() {
    this.canvas.width = window.innerWidth;
    this.canvas.height = window.innerHeight;
  }
  
  createStars() {
    const starCount = Math.floor((this.canvas.width * this.canvas.height) / 10000);
    
    for (let i = 0; i < starCount; i++) {
      this.stars.push({
        x: Math.random() * this.canvas.width,
        y: Math.random() * this.canvas.height,
        size: Math.random() * 2 + 0.5,
        opacity: Math.random() * 0.8 + 0.2,
        twinkleSpeed: Math.random() * 0.02 + 0.01,
        originalOpacity: Math.random() * 0.8 + 0.2
      });
    }
  }
  
  animate() {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    this.stars.forEach(star => {
      // Twinkling effect
      star.opacity += Math.sin(Date.now() * star.twinkleSpeed) * 0.1;
      star.opacity = Math.max(0.1, Math.min(1, star.opacity));
      
      // Mouse interaction
      const dx = this.mouse.x - star.x;
      const dy = this.mouse.y - star.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      
      if (distance < 150) {
        const force = (150 - distance) / 150;
        star.opacity = star.originalOpacity + force * 0.5;
        star.size = (Math.random() * 2 + 0.5) + force * 2;
      } else {
        star.size = Math.random() * 2 + 0.5;
      }
      
      // Draw star
      this.ctx.beginPath();
      this.ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
      this.ctx.fillStyle = `rgba(255, 255, 255, ${star.opacity})`;
      this.ctx.fill();
    });
    
    requestAnimationFrame(() => this.animate());
  }
}

// ===== NAVIGATION =====
class Navigation {
  constructor() {
    this.navbar = document.getElementById('navbar');
    this.navToggle = document.getElementById('nav-toggle');
    this.navMenu = document.getElementById('nav-menu');
    this.navLinks = document.querySelectorAll('.nav-link');
    this.themeToggle = document.getElementById('theme-toggle');
    
    this.init();
  }
  
  init() {
    this.setupScrollEffect();
    this.setupMobileMenu();
    this.setupSmoothScroll();
    this.setupThemeToggle();
  }
  
  setupScrollEffect() {
    const handleScroll = throttle(() => {
      const scrolled = window.scrollY > 50;
      this.navbar.classList.toggle('scrolled', scrolled);
    }, 10);
    
    window.addEventListener('scroll', handleScroll);
  }
  
  setupMobileMenu() {
    this.navToggle.addEventListener('click', () => {
      this.navToggle.classList.toggle('active');
      this.navMenu.classList.toggle('active');
    });
    
    this.navLinks.forEach(link => {
      link.addEventListener('click', () => {
        this.navToggle.classList.remove('active');
        this.navMenu.classList.remove('active');
      });
    });
  }
  
  setupSmoothScroll() {
    this.navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href');
        const targetSection = document.querySelector(targetId);
        
        if (targetSection) {
          const offsetTop = targetSection.offsetTop - 70;
          window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
          });
        }
      });
    });
  }
  
  setupThemeToggle() {
    this.themeToggle.addEventListener('click', () => {
      currentTheme = currentTheme === 'light' ? 'dark' : 'light';
      document.documentElement.setAttribute('data-theme', currentTheme);
      
      const icon = this.themeToggle.querySelector('i');
      icon.className = currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
      
      localStorage.setItem('theme', currentTheme);
    });
    
    // Load saved theme
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      currentTheme = savedTheme;
      document.documentElement.setAttribute('data-theme', currentTheme);
      const icon = this.themeToggle.querySelector('i');
      icon.className = currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
    }
  }
}

// ===== SCROLL ANIMATIONS =====
class ScrollAnimations {
  constructor() {
    this.observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };
    
    this.init();
  }
  
  init() {
    this.setupIntersectionObserver();
    this.setupCounterAnimations();
  }
  
  setupIntersectionObserver() {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');
        }
      });
    }, this.observerOptions);
    
    const animatedElements = document.querySelectorAll(
      '.service-card, .stat-card, .tech-layer, .contact-item, .section-header'
    );
    
    animatedElements.forEach((el, index) => {
      el.classList.add('fade-in');
      el.style.transitionDelay = `${index * 0.1}s`;
      observer.observe(el);
    });
  }
  
  setupCounterAnimations() {
    const counters = document.querySelectorAll('.stat-number');
    
    const animateCounter = (element) => {
      const target = parseInt(element.getAttribute('data-target'));
      const duration = 2000;
      const increment = target / (duration / 16);
      let current = 0;
      
      const updateCounter = () => {
        current += increment;
        if (current < target) {
          element.textContent = Math.floor(current);
          requestAnimationFrame(updateCounter);
        } else {
          element.textContent = target;
        }
      };
      
      updateCounter();
    };
    
    const counterObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          animateCounter(entry.target);
          counterObserver.unobserve(entry.target);
        }
      });
    }, { threshold: 0.5 });
    
    counters.forEach(counter => {
      counterObserver.observe(counter);
    });
  }
}

// ===== HERO INTERACTIONS =====
class HeroInteractions {
  constructor() {
    this.exploreBtn = document.getElementById('explore-btn');
    this.learnMoreBtn = document.getElementById('learn-more');
    this.floatingCards = document.querySelectorAll('.floating-card');
    
    this.init();
  }
  
  init() {
    this.setupButtonInteractions();
    this.setupFloatingCardInteractions();
  }
  
  setupButtonInteractions() {
    this.exploreBtn.addEventListener('click', () => {
      document.querySelector('#about').scrollIntoView({
        behavior: 'smooth'
      });
    });
    
    this.learnMoreBtn.addEventListener('click', () => {
      this.showModal('了解更多', '云深处致力于打造最前沿的云端科技解决方案...');
    });
  }
  
  setupFloatingCardInteractions() {
    this.floatingCards.forEach(card => {
      card.addEventListener('click', () => {
        const tech = card.getAttribute('data-tech');
        this.showTechInfo(tech);
      });
    });
  }
  
  showTechInfo(tech) {
    const techInfo = {
      'AI': '人工智能技术帮助企业实现智能化转型，提供深度学习、机器学习等解决方案。',
      'Cloud': '云计算服务提供弹性、安全、高效的云基础设施，支持企业快速扩展。',
      'IoT': '物联网技术连接万物，实现设备间的智能通信和数据交换。',
      'Blockchain': '区块链技术确保数据安全和透明，为企业提供可信的数字化解决方案。'
    };
    
    this.showModal(tech, techInfo[tech] || '暂无详细信息');
  }
  
  showModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3>${title}</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <p>${content}</p>
        </div>
      </div>
    `;
    
    // Add modal styles
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      opacity: 0;
      transition: opacity 0.3s ease;
    `;
    
    const modalContent = modal.querySelector('.modal-content');
    modalContent.style.cssText = `
      background: white;
      border-radius: 1rem;
      padding: 2rem;
      max-width: 500px;
      width: 90%;
      transform: scale(0.8);
      transition: transform 0.3s ease;
    `;
    
    const modalHeader = modal.querySelector('.modal-header');
    modalHeader.style.cssText = `
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    `;
    
    const modalClose = modal.querySelector('.modal-close');
    modalClose.style.cssText = `
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      color: #666;
    `;
    
    document.body.appendChild(modal);
    
    // Animate in
    setTimeout(() => {
      modal.style.opacity = '1';
      modalContent.style.transform = 'scale(1)';
    }, 10);
    
    // Close handlers
    const closeModal = () => {
      modal.style.opacity = '0';
      modalContent.style.transform = 'scale(0.8)';
      setTimeout(() => modal.remove(), 300);
    };
    
    modalClose.addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => {
      if (e.target === modal) closeModal();
    });
  }
}

// ===== FORM HANDLER =====
class FormHandler {
  constructor() {
    this.contactForm = document.getElementById('contact-form');
    this.init();
  }
  
  init() {
    if (!this.contactForm) return;
    
    this.setupFormSubmission();
  }
  
  setupFormSubmission() {
    this.contactForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const submitBtn = this.contactForm.querySelector('.btn');
      const originalText = submitBtn.innerHTML;
      
      submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发送中...';
      submitBtn.disabled = true;
      
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        this.showMessage('success', '消息发送成功！我们会尽快与您联系。');
        this.contactForm.reset();
      } catch (error) {
        this.showMessage('error', '发送失败，请稍后重试。');
      } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
      }
    });
  }
  
  showMessage(type, text) {
    const message = document.createElement('div');
    message.className = `message message-${type}`;
    message.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'success' ? '#10b981' : '#ef4444'};
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      z-index: 9999;
      transform: translateX(100%);
      transition: transform 0.3s ease;
    `;
    message.textContent = text;
    
    document.body.appendChild(message);
    
    setTimeout(() => {
      message.style.transform = 'translateX(0)';
    }, 10);
    
    setTimeout(() => {
      message.style.transform = 'translateX(100%)';
      setTimeout(() => message.remove(), 300);
    }, 3000);
  }
}

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
  // Initialize components
  const navigation = new Navigation();
  const scrollAnimations = new ScrollAnimations();
  const heroInteractions = new HeroInteractions();
  const formHandler = new FormHandler();
  
  // Initialize stars animation
  const starsCanvas = document.getElementById('stars-canvas');
  if (starsCanvas) {
    new StarsAnimation(starsCanvas);
  }
  
  // Add keyboard navigation support
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      const navMenu = document.getElementById('nav-menu');
      const navToggle = document.getElementById('nav-toggle');
      navMenu.classList.remove('active');
      navToggle.classList.remove('active');
      
      // Close any open modals
      const modals = document.querySelectorAll('.modal-overlay');
      modals.forEach(modal => modal.remove());
    }
  });
  
  // Add focus management for accessibility
  const focusableElements = document.querySelectorAll(
    'a, button, input, textarea, [tabindex]:not([tabindex="-1"])'
  );
  
  focusableElements.forEach(el => {
    el.addEventListener('focus', () => {
      el.style.outline = '2px solid var(--primary-color)';
      el.style.outlineOffset = '2px';
    });
    
    el.addEventListener('blur', () => {
      el.style.outline = 'none';
    });
  });
});

// ===== WINDOW EVENTS =====
window.addEventListener('load', () => {
  // Performance optimization
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      console.log('云深处页面加载完成');
    });
  }
});

// ===== PERFORMANCE MONITORING =====
if ('performance' in window) {
  window.addEventListener('load', () => {
    setTimeout(() => {
      const perfData = performance.getEntriesByType('navigation')[0];
      console.log(`页面加载时间: ${perfData.loadEventEnd - perfData.fetchStart}ms`);
    }, 0);
  });
}
