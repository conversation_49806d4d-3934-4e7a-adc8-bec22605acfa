/* ===== CSS VARIABLES ===== */
:root {
  /* Colors */
  --primary-color: #4f46e5;
  --primary-dark: #3730a3;
  --primary-light: #6366f1;
  --secondary-color: #06b6d4;
  --accent-color: #f59e0b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  
  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-chinese: 'Noto Sans SC', sans-serif;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 6rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-base: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
}

/* Dark theme */
[data-theme="dark"] {
  --white: #111827;
  --gray-50: #1f2937;
  --gray-100: #374151;
  --gray-200: #4b5563;
  --gray-300: #6b7280;
  --gray-400: #9ca3af;
  --gray-500: #d1d5db;
  --gray-600: #e5e7eb;
  --gray-700: #f3f4f6;
  --gray-800: #f9fafb;
  --gray-900: #ffffff;
}

/* ===== RESET & BASE ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--gray-900);
  background-color: var(--white);
  overflow-x: hidden;
  transition: background-color var(--transition-base), color var(--transition-base);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* ===== NAVIGATION ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: all var(--transition-base);
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-lg);
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  text-decoration: none;
}

.brand-icon {
  font-size: 2rem;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: var(--spacing-xl);
}

.nav-link {
  color: var(--gray-700);
  text-decoration: none;
  font-weight: 500;
  padding: var(--spacing-sm) 0;
  position: relative;
  transition: color var(--transition-base);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-color);
  transition: width var(--transition-base);
}

.nav-link:hover {
  color: var(--primary-color);
}

.nav-link:hover::after {
  width: 100%;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.theme-toggle {
  width: 40px;
  height: 40px;
  border: none;
  background: var(--gray-100);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-base);
}

.theme-toggle:hover {
  background: var(--gray-200);
  transform: scale(1.1);
}

.nav-toggle {
  display: none;
  flex-direction: column;
  width: 30px;
  height: 30px;
  cursor: pointer;
  justify-content: space-between;
}

.nav-toggle span {
  width: 100%;
  height: 3px;
  background: var(--gray-700);
  border-radius: var(--radius-full);
  transition: all var(--transition-base);
}

/* ===== HERO SECTION ===== */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

#stars-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.cloud-animation {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.cloud {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-full);
  animation: float-cloud 20s infinite linear;
}

.cloud-1 {
  width: 100px;
  height: 40px;
  top: 20%;
  left: -100px;
  animation-delay: 0s;
}

.cloud-2 {
  width: 80px;
  height: 30px;
  top: 40%;
  left: -80px;
  animation-delay: 5s;
}

.cloud-3 {
  width: 120px;
  height: 50px;
  top: 60%;
  left: -120px;
  animation-delay: 10s;
}

.cloud-4 {
  width: 90px;
  height: 35px;
  top: 80%;
  left: -90px;
  animation-delay: 15s;
}

@keyframes float-cloud {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(100vw + 200px));
  }
}

.hero-content {
  position: relative;
  z-index: 1;
  width: 100%;
  color: var(--white);
}

.hero-content .container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4xl);
  align-items: center;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: var(--spacing-lg);
  font-family: var(--font-chinese);
}

.title-word {
  display: inline-block;
  position: relative;
  margin-right: var(--spacing-sm);
  animation: title-reveal 1s ease-out forwards;
  opacity: 0;
  transform: translateY(50px);
}

.title-word:nth-child(1) { animation-delay: 0.2s; }
.title-word:nth-child(2) { animation-delay: 0.4s; }
.title-word:nth-child(3) { animation-delay: 0.6s; }

@keyframes title-reveal {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-lg);
  opacity: 0.9;
  font-family: var(--font-chinese);
}

.hero-description {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: var(--spacing-2xl);
  opacity: 0.8;
  font-family: var(--font-chinese);
}

.hero-actions {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--radius-full);
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: rgba(255, 255, 255, 0.2);
  color: var(--white);
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.btn-primary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.btn-secondary {
  background: transparent;
  color: var(--white);
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.hero-visual {
  position: relative;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-card {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--white);
  animation: float-card 6s ease-in-out infinite;
  cursor: pointer;
  transition: all var(--transition-base);
}

.floating-card:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.floating-card:nth-child(1) {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.floating-card:nth-child(2) {
  top: 20%;
  right: 15%;
  animation-delay: 1.5s;
}

.floating-card:nth-child(3) {
  bottom: 20%;
  left: 20%;
  animation-delay: 3s;
}

.floating-card:nth-child(4) {
  bottom: 10%;
  right: 10%;
  animation-delay: 4.5s;
}

.floating-card i {
  font-size: 2rem;
  margin-bottom: var(--spacing-sm);
}

@keyframes float-card {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(2deg);
  }
  66% {
    transform: translateY(-10px) rotate(-1deg);
  }
}

.central-orb {
  position: relative;
  width: 200px;
  height: 200px;
}

.orb-core {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.2) 100%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 2s ease-in-out infinite;
}

.orb-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: rotate 10s linear infinite;
}

.ring-1 {
  width: 100px;
  height: 100px;
  animation-duration: 8s;
}

.ring-2 {
  width: 140px;
  height: 140px;
  animation-duration: 12s;
  animation-direction: reverse;
}

.ring-3 {
  width: 180px;
  height: 180px;
  animation-duration: 15s;
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.7;
  }
}

@keyframes rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.scroll-indicator {
  position: absolute;
  bottom: var(--spacing-xl);
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: var(--white);
  animation: bounce 2s infinite;
}

.scroll-mouse {
  width: 30px;
  height: 50px;
  border: 2px solid var(--white);
  border-radius: 15px;
  margin: 0 auto var(--spacing-sm);
  position: relative;
}

.scroll-wheel {
  width: 4px;
  height: 10px;
  background: var(--white);
  border-radius: 2px;
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  animation: scroll-wheel 2s infinite;
}

@keyframes scroll-wheel {
  0% {
    top: 8px;
    opacity: 1;
  }
  100% {
    top: 24px;
    opacity: 0;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* ===== SECTIONS ===== */
section {
  padding: var(--spacing-4xl) 0;
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-4xl);
}

.section-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--gray-900);
  margin-bottom: var(--spacing-lg);
  font-family: var(--font-chinese);
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
  font-family: var(--font-chinese);
}

/* ===== ABOUT SECTION ===== */
.about {
  background: var(--gray-50);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4xl);
  align-items: start;
}

.text-block {
  margin-bottom: var(--spacing-2xl);
}

.text-block h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-md);
  font-family: var(--font-chinese);
}

.text-block p {
  color: var(--gray-600);
  line-height: 1.8;
  font-family: var(--font-chinese);
}

.value-list {
  list-style: none;
}

.value-list li {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) 0;
  color: var(--gray-700);
  font-family: var(--font-chinese);
}

.value-list i {
  color: var(--success-color);
  font-size: 0.875rem;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
}

.stat-card {
  background: var(--white);
  padding: var(--spacing-xl);
  border-radius: var(--radius-xl);
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-base);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 1.5rem;
  margin: 0 auto var(--spacing-lg);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary-color);
  display: block;
  margin-bottom: var(--spacing-sm);
}

.stat-label {
  color: var(--gray-600);
  font-weight: 500;
  font-family: var(--font-chinese);
}

/* ===== SERVICES SECTION ===== */
.services {
  background: var(--white);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-xl);
}

.service-card {
  background: var(--white);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-2xl);
}

.service-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 2rem;
  margin-bottom: var(--spacing-lg);
}

.service-card h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-md);
  font-family: var(--font-chinese);
}

.service-card p {
  color: var(--gray-600);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
  font-family: var(--font-chinese);
}

.service-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.feature-tag {
  background: var(--gray-100);
  color: var(--gray-700);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: 0.875rem;
  font-weight: 500;
  font-family: var(--font-chinese);
}

/* ===== TECHNOLOGY SECTION ===== */
.technology {
  background: var(--gray-900);
  color: var(--white);
}

.technology .section-title {
  color: var(--white);
}

.technology .section-subtitle {
  color: var(--gray-300);
}

.tech-showcase {
  max-width: 800px;
  margin: 0 auto;
}

.tech-diagram {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-xl);
}

.tech-layer {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  text-align: center;
  transition: all var(--transition-base);
}

.tech-layer:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-5px);
}

.tech-layer h4 {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: var(--spacing-lg);
  color: var(--white);
  font-family: var(--font-chinese);
}

.tech-items {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.tech-item {
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-full);
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--transition-base);
}

.tech-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

/* ===== CONTACT SECTION ===== */
.contact {
  background: var(--gray-50);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4xl);
  align-items: start;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-lg);
}

.contact-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 1.25rem;
  flex-shrink: 0;
}

.contact-details h4 {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
  font-family: var(--font-chinese);
}

.contact-details p {
  color: var(--gray-600);
  font-family: var(--font-chinese);
}

.contact-form {
  background: var(--white);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  font-family: inherit;
  transition: all var(--transition-base);
  background: var(--white);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.contact-form .btn {
  width: 100%;
  justify-content: center;
}

/* ===== FOOTER ===== */
.footer {
  background: var(--gray-900);
  color: var(--white);
  padding: var(--spacing-4xl) 0 var(--spacing-xl);
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
}

.footer-brand {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.footer-brand .brand-icon {
  font-size: 2rem;
}

.footer-brand h3 {
  font-size: 1.5rem;
  font-weight: 700;
  font-family: var(--font-chinese);
}

.footer-brand p {
  color: var(--gray-400);
  font-family: var(--font-chinese);
}

.link-group h4 {
  font-size: 1.125rem;
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  color: var(--white);
  font-family: var(--font-chinese);
}

.link-group a {
  display: block;
  color: var(--gray-400);
  text-decoration: none;
  margin-bottom: var(--spacing-sm);
  transition: color var(--transition-base);
  font-family: var(--font-chinese);
}

.link-group a:hover {
  color: var(--white);
}

.footer-bottom {
  border-top: 1px solid var(--gray-800);
  padding-top: var(--spacing-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.footer-bottom p {
  color: var(--gray-400);
  font-family: var(--font-chinese);
}

.social-links {
  display: flex;
  gap: var(--spacing-md);
}

.social-links a {
  width: 40px;
  height: 40px;
  background: var(--gray-800);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-400);
  text-decoration: none;
  transition: all var(--transition-base);
}

.social-links a:hover {
  background: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .hero-content .container {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    text-align: center;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }
}

@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background: var(--white);
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: var(--spacing-2xl);
    transition: left var(--transition-base);
    box-shadow: var(--shadow-lg);
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-toggle {
    display: flex;
  }

  .hero-title {
    font-size: 3rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .about-stats {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-md);
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .floating-card {
    padding: var(--spacing-md);
    font-size: 0.875rem;
  }

  .floating-card i {
    font-size: 1.5rem;
  }
}

/* ===== ANIMATIONS ===== */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.slide-in-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: all 0.6s ease-out;
}

.slide-in-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-in-right {
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.6s ease-out;
}

.slide-in-right.visible {
  opacity: 1;
  transform: translateX(0);
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
