/* ===== CSS VARIABLES ===== */
:root {
  /* Colors */
  --primary-color: #667eea;
  --primary-dark: #5a6fd8;
  --primary-light: #7c8ef0;
  --secondary-color: #764ba2;
  --accent-color: #f093fb;
  --success-color: #4ade80;
  --warning-color: #fbbf24;
  --error-color: #ef4444;
  
  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --black: #000000;
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;
  
  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-base: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
  
  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

/* Dark theme variables */
[data-theme="dark"] {
  --white: #111827;
  --gray-50: #1f2937;
  --gray-100: #374151;
  --gray-200: #4b5563;
  --gray-300: #6b7280;
  --gray-400: #9ca3af;
  --gray-500: #d1d5db;
  --gray-600: #e5e7eb;
  --gray-700: #f3f4f6;
  --gray-800: #f9fafb;
  --gray-900: #ffffff;
  --black: #ffffff;
}

/* ===== RESET & BASE STYLES ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--gray-900);
  background-color: var(--white);
  overflow-x: hidden;
  transition: background-color var(--transition-base), color var(--transition-base);
}

/* ===== LOADING SCREEN ===== */
#loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.5s ease-out, visibility 0.5s ease-out;
}

#loading-screen.hidden {
  opacity: 0;
  visibility: hidden;
}

.loading-content {
  text-align: center;
  color: var(--white);
}

.loading-logo {
  margin-bottom: var(--spacing-8);
}

.cloud-icon {
  font-size: var(--font-size-6xl);
  margin-bottom: var(--spacing-4);
  animation: float 3s ease-in-out infinite;
}

.loading-logo h2 {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  margin-bottom: var(--spacing-2);
}

.loading-bar {
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin: var(--spacing-6) auto;
}

.loading-progress {
  height: 100%;
  background: var(--white);
  border-radius: var(--radius-full);
  animation: loading-progress 2s ease-in-out infinite;
}

.loading-text {
  font-size: var(--font-size-lg);
  opacity: 0.9;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes loading-progress {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

/* ===== CURSOR FOLLOWER ===== */
.cursor-follower {
  position: fixed;
  width: 20px;
  height: 20px;
  background: var(--primary-color);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9998;
  transition: transform 0.1s ease-out;
  opacity: 0;
  mix-blend-mode: difference;
}

.cursor-follower.active {
  opacity: 1;
  transform: scale(1.5);
}

/* ===== NAVIGATION ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: var(--z-fixed);
  transition: all var(--transition-base);
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-lg);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
  text-decoration: none;
}

.brand-icon {
  font-size: var(--font-size-2xl);
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: var(--spacing-8);
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;
}

.nav-link {
  position: relative;
  color: var(--gray-700);
  text-decoration: none;
  font-weight: 500;
  font-size: var(--font-size-base);
  padding: var(--spacing-2) 0;
  transition: color var(--transition-base);
  overflow: hidden;
}

.nav-link::before {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transform: translateY(100%);
  transition: transform var(--transition-base);
}

.nav-link:hover::before {
  transform: translateY(0);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  transition: width var(--transition-base);
}

.nav-link:hover::after {
  width: 100%;
}

/* Dropdown Menu */
.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-4);
  min-width: 200px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-base);
}

.dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: block;
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--gray-700);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.dropdown-item:hover {
  background: var(--gray-50);
  color: var(--primary-color);
}

/* Navigation Actions */
.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.theme-toggle {
  width: 40px;
  height: 40px;
  border: none;
  background: var(--gray-100);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-base);
}

.theme-toggle:hover {
  background: var(--gray-200);
  transform: scale(1.1);
}

.nav-toggle {
  display: none;
  flex-direction: column;
  width: 30px;
  height: 30px;
  cursor: pointer;
  justify-content: space-between;
}

.nav-toggle span {
  width: 100%;
  height: 3px;
  background: var(--gray-700);
  border-radius: var(--radius-full);
  transition: all var(--transition-base);
}

.nav-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(8px, 8px);
}

.nav-toggle.active span:nth-child(2) {
  opacity: 0;
}

.nav-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(8px, -8px);
}

/* ===== UTILITY CLASSES ===== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-4) var(--spacing-6);
  border: none;
  border-radius: var(--radius-full);
  font-size: var(--font-size-base);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--white);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
  background: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: var(--gray-700);
  border: 2px solid var(--gray-300);
}

.btn-outline:hover {
  background: var(--gray-900);
  color: var(--white);
  border-color: var(--gray-900);
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: var(--spacing-16);
}

.section-tag {
  display: inline-block;
  padding: var(--spacing-2) var(--spacing-4);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--white);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: 600;
  margin-bottom: var(--spacing-4);
}

.section-title {
  font-size: var(--font-size-4xl);
  font-weight: 800;
  color: var(--gray-900);
  margin-bottom: var(--spacing-6);
  line-height: 1.2;
}

.title-highlight {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-description {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.7;
}

/* ===== HERO SECTION ===== */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

#particle-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    rgba(102, 126, 234, 0.9) 0%,
    rgba(118, 75, 162, 0.8) 50%,
    rgba(240, 147, 251, 0.7) 100%);
}

.hero-content {
  position: relative;
  z-index: 1;
  width: 100%;
  padding: var(--spacing-20) 0;
}

.hero-content .container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-16);
  align-items: center;
}

.hero-text {
  color: var(--white);
}

.hero-title {
  font-size: var(--font-size-6xl);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: var(--spacing-6);
}

.title-line {
  display: block;
  position: relative;
  overflow: hidden;
}

.title-line::before {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #fff 0%, #f0f9ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transform: translateX(-100%);
  animation: text-reveal 1s ease-out forwards;
}

.title-line:nth-child(2)::before {
  animation-delay: 0.3s;
}

@keyframes text-reveal {
  to {
    transform: translateX(0);
  }
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  line-height: 1.6;
  margin-bottom: var(--spacing-8);
  opacity: 0.9;
}

.hero-stats {
  display: flex;
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-10);
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--font-size-3xl);
  font-weight: 800;
  margin-bottom: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-sm);
  opacity: 0.8;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

.hero-visual {
  position: relative;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-card {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  padding: var(--spacing-4);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--white);
  animation: float-card 6s ease-in-out infinite;
}

.floating-card:nth-child(1) {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.floating-card:nth-child(2) {
  top: 20%;
  right: 15%;
  animation-delay: 1.5s;
}

.floating-card:nth-child(3) {
  bottom: 20%;
  left: 20%;
  animation-delay: 3s;
}

.floating-card:nth-child(4) {
  bottom: 10%;
  right: 10%;
  animation-delay: 4.5s;
}

.floating-card i {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-2);
}

@keyframes float-card {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(2deg);
  }
  66% {
    transform: translateY(-10px) rotate(-1deg);
  }
}

.hero-3d-object {
  position: relative;
  perspective: 1000px;
}

.cube {
  width: 200px;
  height: 200px;
  position: relative;
  transform-style: preserve-3d;
  animation: rotate-cube 20s linear infinite;
}

.face {
  position: absolute;
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.face.front { transform: rotateY(0deg) translateZ(100px); }
.face.back { transform: rotateY(180deg) translateZ(100px); }
.face.right { transform: rotateY(90deg) translateZ(100px); }
.face.left { transform: rotateY(-90deg) translateZ(100px); }
.face.top { transform: rotateX(90deg) translateZ(100px); }
.face.bottom { transform: rotateX(-90deg) translateZ(100px); }

@keyframes rotate-cube {
  0% { transform: rotateX(0deg) rotateY(0deg); }
  100% { transform: rotateX(360deg) rotateY(360deg); }
}

.scroll-indicator {
  position: absolute;
  bottom: var(--spacing-8);
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: var(--white);
  animation: bounce 2s infinite;
}

.scroll-arrow {
  width: 40px;
  height: 40px;
  border: 2px solid var(--white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-2);
}

.scroll-text {
  font-size: var(--font-size-sm);
  opacity: 0.8;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* ===== SOLUTIONS SECTION ===== */
.solutions {
  padding: var(--spacing-24) 0;
  background: var(--gray-50);
}

.solutions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-8);
}

.solution-card {
  background: var(--white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.solution-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.solution-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-2xl);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-6);
}

.card-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: var(--font-size-2xl);
}

.card-badge {
  padding: var(--spacing-1) var(--spacing-3);
  background: var(--accent-color);
  color: var(--white);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-4);
}

.card-description {
  color: var(--gray-600);
  line-height: 1.6;
  margin-bottom: var(--spacing-6);
}

.card-features {
  list-style: none;
  margin-bottom: var(--spacing-6);
}

.card-features li {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-2) 0;
  color: var(--gray-700);
}

.card-features i {
  color: var(--success-color);
  font-size: var(--font-size-sm);
}

.card-footer {
  border-top: 1px solid var(--gray-200);
  padding-top: var(--spacing-4);
}

.card-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  transition: all var(--transition-base);
}

.card-link:hover {
  gap: var(--spacing-3);
}

/* ===== PRODUCTS SECTION ===== */
.products {
  padding: var(--spacing-24) 0;
  background: var(--white);
}

.products-carousel {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
}

.product-slide {
  display: none;
  opacity: 0;
  transition: opacity var(--transition-slow);
}

.product-slide.active {
  display: block;
  opacity: 1;
}

.product-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-12);
  align-items: center;
}

.product-info h3 {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-4);
}

.product-info p {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  line-height: 1.6;
  margin-bottom: var(--spacing-6);
}

.product-features {
  list-style: none;
  margin-bottom: var(--spacing-8);
}

.product-features li {
  padding: var(--spacing-2) 0;
  color: var(--gray-700);
  position: relative;
  padding-left: var(--spacing-6);
}

.product-features li::before {
  content: '→';
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

.product-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.product-mockup {
  position: relative;
  width: 400px;
  height: 300px;
  background: var(--gray-900);
  border-radius: var(--radius-xl);
  padding: var(--spacing-4);
  box-shadow: var(--shadow-2xl);
}

.mockup-screen {
  width: 100%;
  height: 100%;
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  position: relative;
  overflow: hidden;
}

.mockup-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.dashboard-header {
  height: 40px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-md);
  opacity: 0.8;
}

.dashboard-charts {
  flex: 1;
  background: var(--gray-100);
  border-radius: var(--radius-md);
  position: relative;
}

.dashboard-charts::before {
  content: '';
  position: absolute;
  top: 20%;
  left: 10%;
  width: 80%;
  height: 60%;
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  border-radius: var(--radius-sm);
  opacity: 0.3;
}

.dashboard-stats {
  height: 60px;
  background: var(--gray-50);
  border-radius: var(--radius-md);
  display: flex;
  gap: var(--spacing-2);
}

.dashboard-stats::before,
.dashboard-stats::after {
  content: '';
  flex: 1;
  background: var(--success-color);
  border-radius: var(--radius-sm);
  margin: var(--spacing-2);
  opacity: 0.6;
}

.dashboard-stats::after {
  background: var(--warning-color);
}

.carousel-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-6);
  margin-top: var(--spacing-8);
}

.carousel-btn {
  width: 50px;
  height: 50px;
  border: none;
  background: var(--gray-100);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-base);
}

.carousel-btn:hover {
  background: var(--primary-color);
  color: var(--white);
  transform: scale(1.1);
}

.carousel-dots {
  display: flex;
  gap: var(--spacing-2);
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--gray-300);
  cursor: pointer;
  transition: all var(--transition-base);
}

.dot.active {
  background: var(--primary-color);
  transform: scale(1.2);
}

/* ===== CASES SECTION ===== */
.cases {
  padding: var(--spacing-24) 0;
  background: var(--gray-50);
}

.cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-8);
}

.case-card {
  background: var(--white);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-base);
}

.case-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-2xl);
}

.case-image {
  height: 200px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.case-overlay {
  color: var(--white);
  font-size: var(--font-size-4xl);
  opacity: 0.8;
}

.case-content {
  padding: var(--spacing-6);
}

.case-content h3 {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-4);
}

.case-content p {
  color: var(--gray-600);
  line-height: 1.6;
  margin-bottom: var(--spacing-6);
}

.case-metrics {
  display: flex;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-6);
}

.metric {
  text-align: center;
}

.metric-value {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: var(--spacing-1);
}

.metric-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.case-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  transition: all var(--transition-base);
}

.case-link:hover {
  gap: var(--spacing-3);
}

/* ===== ABOUT SECTION ===== */
.about {
  padding: var(--spacing-24) 0;
  background: var(--white);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-16);
  align-items: center;
}

.about-text .section-header {
  text-align: left;
  margin-bottom: var(--spacing-8);
}

.about-description {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  line-height: 1.7;
  margin-bottom: var(--spacing-8);
}

.about-highlights {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.highlight-item i {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.highlight-item h4 {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.highlight-item p {
  color: var(--gray-600);
  font-size: var(--font-size-base);
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-6);
}

.team-member {
  text-align: center;
  padding: var(--spacing-6);
  background: var(--gray-50);
  border-radius: var(--radius-xl);
  transition: all var(--transition-base);
}

.team-member:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.member-avatar {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: var(--font-size-2xl);
  margin: 0 auto var(--spacing-4);
}

.team-member h4 {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.team-member p {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}

/* ===== CONTACT SECTION ===== */
.contact {
  padding: var(--spacing-24) 0;
  background: var(--gray-50);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-16);
  margin-top: var(--spacing-12);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-4);
}

.contact-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.contact-details h4 {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.contact-details p {
  color: var(--gray-600);
  font-size: var(--font-size-base);
}

.contact-form {
  background: var(--white);
  padding: var(--spacing-8);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.form-group {
  margin-bottom: var(--spacing-4);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-4);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-family: inherit;
  transition: all var(--transition-base);
  background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.contact-form .btn {
  width: 100%;
  justify-content: center;
  margin-top: var(--spacing-4);
}

/* ===== FOOTER ===== */
.footer {
  background: var(--gray-900);
  color: var(--white);
  padding: var(--spacing-16) 0 var(--spacing-8);
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: var(--spacing-12);
  margin-bottom: var(--spacing-12);
}

.footer-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.footer-brand .brand-icon {
  font-size: var(--font-size-2xl);
}

.footer-brand h3 {
  font-size: var(--font-size-xl);
  font-weight: 700;
}

.footer-section p {
  color: var(--gray-400);
  line-height: 1.6;
  margin-bottom: var(--spacing-6);
}

.footer-section h4 {
  font-size: var(--font-size-lg);
  font-weight: 700;
  margin-bottom: var(--spacing-4);
  color: var(--white);
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: var(--spacing-3);
}

.footer-section ul li a {
  color: var(--gray-400);
  text-decoration: none;
  transition: color var(--transition-base);
}

.footer-section ul li a:hover {
  color: var(--white);
}

.social-links {
  display: flex;
  gap: var(--spacing-4);
}

.social-links a {
  width: 40px;
  height: 40px;
  background: var(--gray-800);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-400);
  text-decoration: none;
  transition: all var(--transition-base);
}

.social-links a:hover {
  background: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
}

.footer-bottom {
  border-top: 1px solid var(--gray-800);
  padding-top: var(--spacing-8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-4);
}

.footer-copyright p {
  color: var(--gray-400);
  margin: 0;
}

.footer-links {
  display: flex;
  gap: var(--spacing-6);
}

.footer-links a {
  color: var(--gray-400);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: color var(--transition-base);
}

.footer-links a:hover {
  color: var(--white);
}

/* ===== BACK TO TOP ===== */
.back-to-top {
  position: fixed;
  bottom: var(--spacing-6);
  right: var(--spacing-6);
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  border: none;
  border-radius: 50%;
  color: var(--white);
  font-size: var(--font-size-lg);
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all var(--transition-base);
  z-index: var(--z-fixed);
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.back-to-top:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .hero-content .container {
    grid-template-columns: 1fr;
    gap: var(--spacing-12);
    text-align: center;
  }

  .hero-visual {
    height: 400px;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-12);
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-12);
  }

  .product-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
  }
}

@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 80px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 80px);
    background: var(--white);
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: var(--spacing-8);
    transition: left var(--transition-base);
    box-shadow: var(--shadow-lg);
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-toggle {
    display: flex;
  }

  .hero-title {
    font-size: var(--font-size-4xl);
  }

  .hero-stats {
    flex-direction: column;
    gap: var(--spacing-4);
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .section-title {
    font-size: var(--font-size-3xl);
  }

  .solutions-grid {
    grid-template-columns: 1fr;
  }

  .cases-grid {
    grid-template-columns: 1fr;
  }

  .team-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
    text-align: center;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-4);
  }

  .nav-container {
    padding: 0 var(--spacing-4);
  }

  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .section-title {
    font-size: var(--font-size-2xl);
  }

  .floating-card {
    padding: var(--spacing-3);
    font-size: var(--font-size-sm);
  }

  .floating-card i {
    font-size: var(--font-size-lg);
  }
}

/* ===== ANIMATIONS ===== */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.slide-in-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: all 0.6s ease-out;
}

.slide-in-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-in-right {
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.6s ease-out;
}

.slide-in-right.visible {
  opacity: 1;
  transform: translateX(0);
}

.scale-in {
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.6s ease-out;
}

.scale-in.visible {
  opacity: 1;
  transform: scale(1);
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ===== PRINT STYLES ===== */
@media print {
  .navbar,
  .back-to-top,
  .scroll-indicator {
    display: none !important;
  }

  .hero-background {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}
