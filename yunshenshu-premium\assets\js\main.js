// ===== GLOBAL VARIABLES =====
let isLoading = true;
let currentTheme = 'light';
let particleSystem = null;
let scrollProgress = 0;

// ===== UTILITY FUNCTIONS =====
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }
};

const lerp = (start, end, factor) => {
  return start + (end - start) * factor;
};

const clamp = (value, min, max) => {
  return Math.min(Math.max(value, min), max);
};

// ===== LOADING SCREEN =====
class LoadingScreen {
  constructor() {
    this.loadingScreen = document.getElementById('loading-screen');
    this.progressBar = document.querySelector('.loading-progress');
    this.loadingText = document.querySelector('.loading-text');
    this.progress = 0;
    this.targetProgress = 0;
    
    this.init();
  }
  
  init() {
    this.simulateLoading();
    this.animateProgress();
  }
  
  simulateLoading() {
    const steps = [
      { progress: 20, text: '加载资源中...' },
      { progress: 40, text: '初始化组件...' },
      { progress: 60, text: '准备动画效果...' },
      { progress: 80, text: '优化性能...' },
      { progress: 100, text: '准备就绪！' }
    ];
    
    let currentStep = 0;
    
    const loadStep = () => {
      if (currentStep < steps.length) {
        const step = steps[currentStep];
        this.targetProgress = step.progress;
        this.loadingText.textContent = step.text;
        currentStep++;
        
        setTimeout(loadStep, 500 + Math.random() * 300);
      } else {
        setTimeout(() => this.hide(), 500);
      }
    };
    
    setTimeout(loadStep, 300);
  }
  
  animateProgress() {
    const animate = () => {
      this.progress = lerp(this.progress, this.targetProgress, 0.1);
      this.progressBar.style.width = `${this.progress}%`;
      
      if (Math.abs(this.progress - this.targetProgress) > 0.1) {
        requestAnimationFrame(animate);
      }
    };
    
    animate();
  }
  
  hide() {
    this.loadingScreen.classList.add('hidden');
    setTimeout(() => {
      this.loadingScreen.style.display = 'none';
      isLoading = false;
      document.body.style.overflow = 'auto';
    }, 500);
  }
}

// ===== CURSOR FOLLOWER =====
class CursorFollower {
  constructor() {
    this.cursor = document.querySelector('.cursor-follower');
    this.mouse = { x: 0, y: 0 };
    this.cursorPos = { x: 0, y: 0 };
    
    this.init();
  }
  
  init() {
    document.addEventListener('mousemove', (e) => {
      this.mouse.x = e.clientX;
      this.mouse.y = e.clientY;
    });
    
    document.addEventListener('mouseenter', () => {
      this.cursor.classList.add('active');
    });
    
    document.addEventListener('mouseleave', () => {
      this.cursor.classList.remove('active');
    });
    
    // Add hover effects for interactive elements
    const interactiveElements = document.querySelectorAll('a, button, .card, .btn');
    interactiveElements.forEach(el => {
      el.addEventListener('mouseenter', () => {
        this.cursor.style.transform = 'scale(2)';
        this.cursor.style.backgroundColor = 'var(--primary-color)';
      });
      
      el.addEventListener('mouseleave', () => {
        this.cursor.style.transform = 'scale(1)';
        this.cursor.style.backgroundColor = 'var(--primary-color)';
      });
    });
    
    this.animate();
  }
  
  animate() {
    this.cursorPos.x = lerp(this.cursorPos.x, this.mouse.x, 0.15);
    this.cursorPos.y = lerp(this.cursorPos.y, this.mouse.y, 0.15);
    
    this.cursor.style.left = `${this.cursorPos.x}px`;
    this.cursor.style.top = `${this.cursorPos.y}px`;
    
    requestAnimationFrame(() => this.animate());
  }
}

// ===== PARTICLE SYSTEM =====
class ParticleSystem {
  constructor(canvas) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.particles = [];
    this.mouse = { x: 0, y: 0 };
    
    this.init();
  }
  
  init() {
    this.resize();
    this.createParticles();
    
    window.addEventListener('resize', () => this.resize());
    document.addEventListener('mousemove', (e) => {
      this.mouse.x = e.clientX;
      this.mouse.y = e.clientY;
    });
    
    this.animate();
  }
  
  resize() {
    this.canvas.width = window.innerWidth;
    this.canvas.height = window.innerHeight;
  }
  
  createParticles() {
    const particleCount = Math.floor((this.canvas.width * this.canvas.height) / 15000);
    
    for (let i = 0; i < particleCount; i++) {
      this.particles.push({
        x: Math.random() * this.canvas.width,
        y: Math.random() * this.canvas.height,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5,
        size: Math.random() * 2 + 1,
        opacity: Math.random() * 0.5 + 0.2,
        originalOpacity: Math.random() * 0.5 + 0.2
      });
    }
  }
  
  animate() {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    this.particles.forEach((particle, index) => {
      // Update position
      particle.x += particle.vx;
      particle.y += particle.vy;
      
      // Wrap around edges
      if (particle.x < 0) particle.x = this.canvas.width;
      if (particle.x > this.canvas.width) particle.x = 0;
      if (particle.y < 0) particle.y = this.canvas.height;
      if (particle.y > this.canvas.height) particle.y = 0;
      
      // Mouse interaction
      const dx = this.mouse.x - particle.x;
      const dy = this.mouse.y - particle.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      
      if (distance < 100) {
        const force = (100 - distance) / 100;
        particle.vx += (dx / distance) * force * 0.01;
        particle.vy += (dy / distance) * force * 0.01;
        particle.opacity = particle.originalOpacity + force * 0.5;
      } else {
        particle.opacity = lerp(particle.opacity, particle.originalOpacity, 0.02);
        particle.vx *= 0.99;
        particle.vy *= 0.99;
      }
      
      // Draw particle
      this.ctx.beginPath();
      this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
      this.ctx.fillStyle = `rgba(255, 255, 255, ${particle.opacity})`;
      this.ctx.fill();
      
      // Draw connections
      this.particles.slice(index + 1).forEach(otherParticle => {
        const dx = particle.x - otherParticle.x;
        const dy = particle.y - otherParticle.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < 80) {
          const opacity = (80 - distance) / 80 * 0.2;
          this.ctx.beginPath();
          this.ctx.moveTo(particle.x, particle.y);
          this.ctx.lineTo(otherParticle.x, otherParticle.y);
          this.ctx.strokeStyle = `rgba(255, 255, 255, ${opacity})`;
          this.ctx.lineWidth = 0.5;
          this.ctx.stroke();
        }
      });
    });
    
    requestAnimationFrame(() => this.animate());
  }
}

// ===== NAVIGATION =====
class Navigation {
  constructor() {
    this.navbar = document.getElementById('navbar');
    this.navToggle = document.getElementById('nav-toggle');
    this.navMenu = document.getElementById('nav-menu');
    this.navLinks = document.querySelectorAll('.nav-link');
    this.themeToggle = document.getElementById('theme-toggle');
    
    this.init();
  }
  
  init() {
    this.setupScrollEffect();
    this.setupMobileMenu();
    this.setupSmoothScroll();
    this.setupThemeToggle();
  }
  
  setupScrollEffect() {
    const handleScroll = throttle(() => {
      const scrolled = window.scrollY > 50;
      this.navbar.classList.toggle('scrolled', scrolled);
    }, 10);
    
    window.addEventListener('scroll', handleScroll);
  }
  
  setupMobileMenu() {
    this.navToggle.addEventListener('click', () => {
      this.navToggle.classList.toggle('active');
      this.navMenu.classList.toggle('active');
    });
    
    this.navLinks.forEach(link => {
      link.addEventListener('click', () => {
        this.navToggle.classList.remove('active');
        this.navMenu.classList.remove('active');
      });
    });
  }
  
  setupSmoothScroll() {
    this.navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href');
        const targetSection = document.querySelector(targetId);
        
        if (targetSection) {
          const offsetTop = targetSection.offsetTop - 80;
          window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
          });
        }
      });
    });
  }
  
  setupThemeToggle() {
    this.themeToggle.addEventListener('click', () => {
      currentTheme = currentTheme === 'light' ? 'dark' : 'light';
      document.documentElement.setAttribute('data-theme', currentTheme);
      
      const icon = this.themeToggle.querySelector('i');
      icon.className = currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
      
      localStorage.setItem('theme', currentTheme);
    });
    
    // Load saved theme
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      currentTheme = savedTheme;
      document.documentElement.setAttribute('data-theme', currentTheme);
      const icon = this.themeToggle.querySelector('i');
      icon.className = currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
    }
  }
}

// ===== SCROLL ANIMATIONS =====
class ScrollAnimations {
  constructor() {
    this.observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };
    
    this.init();
  }
  
  init() {
    this.setupIntersectionObserver();
    this.setupParallaxEffect();
    this.setupScrollProgress();
    this.setupCounterAnimations();
  }
  
  setupIntersectionObserver() {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');
        }
      });
    }, this.observerOptions);
    
    const animatedElements = document.querySelectorAll(
      '.solution-card, .case-card, .team-member, .contact-item, .section-header'
    );
    
    animatedElements.forEach((el, index) => {
      el.classList.add('fade-in');
      el.style.transitionDelay = `${index * 0.1}s`;
      observer.observe(el);
    });
  }
  
  setupParallaxEffect() {
    const parallaxElements = document.querySelectorAll('[data-depth]');
    
    const handleScroll = throttle(() => {
      const scrolled = window.scrollY;
      
      parallaxElements.forEach(el => {
        const depth = parseFloat(el.getAttribute('data-depth'));
        const movement = scrolled * depth;
        el.style.transform = `translateY(${movement}px)`;
      });
    }, 10);
    
    window.addEventListener('scroll', handleScroll);
  }
  
  setupScrollProgress() {
    const progressBar = document.createElement('div');
    progressBar.className = 'scroll-progress';
    progressBar.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 0%;
      height: 3px;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      z-index: 9999;
      transition: width 0.1s ease;
    `;
    document.body.appendChild(progressBar);
    
    const handleScroll = throttle(() => {
      const scrolled = window.scrollY;
      const maxScroll = document.documentElement.scrollHeight - window.innerHeight;
      const progress = (scrolled / maxScroll) * 100;
      progressBar.style.width = `${progress}%`;
    }, 10);
    
    window.addEventListener('scroll', handleScroll);
  }
  
  setupCounterAnimations() {
    const counters = document.querySelectorAll('.stat-number');
    
    const animateCounter = (element) => {
      const target = parseInt(element.getAttribute('data-target'));
      const duration = 2000;
      const increment = target / (duration / 16);
      let current = 0;
      
      const updateCounter = () => {
        current += increment;
        if (current < target) {
          element.textContent = Math.floor(current);
          requestAnimationFrame(updateCounter);
        } else {
          element.textContent = target;
        }
      };
      
      updateCounter();
    };
    
    const counterObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          animateCounter(entry.target);
          counterObserver.unobserve(entry.target);
        }
      });
    }, { threshold: 0.5 });
    
    counters.forEach(counter => {
      counterObserver.observe(counter);
    });
  }
}

// ===== PRODUCT CAROUSEL =====
class ProductCarousel {
  constructor() {
    this.carousel = document.querySelector('.products-carousel');
    this.slides = document.querySelectorAll('.product-slide');
    this.dots = document.querySelectorAll('.dot');
    this.prevBtn = document.querySelector('.carousel-btn.prev');
    this.nextBtn = document.querySelector('.carousel-btn.next');
    this.currentSlide = 0;
    this.autoPlayInterval = null;

    this.init();
  }

  init() {
    if (!this.carousel) return;

    this.setupControls();
    this.startAutoPlay();
    this.setupHoverPause();
  }

  setupControls() {
    this.prevBtn.addEventListener('click', () => this.prevSlide());
    this.nextBtn.addEventListener('click', () => this.nextSlide());

    this.dots.forEach((dot, index) => {
      dot.addEventListener('click', () => this.goToSlide(index));
    });
  }

  goToSlide(index) {
    this.slides[this.currentSlide].classList.remove('active');
    this.dots[this.currentSlide].classList.remove('active');

    this.currentSlide = index;

    this.slides[this.currentSlide].classList.add('active');
    this.dots[this.currentSlide].classList.add('active');
  }

  nextSlide() {
    const nextIndex = (this.currentSlide + 1) % this.slides.length;
    this.goToSlide(nextIndex);
  }

  prevSlide() {
    const prevIndex = (this.currentSlide - 1 + this.slides.length) % this.slides.length;
    this.goToSlide(prevIndex);
  }

  startAutoPlay() {
    this.autoPlayInterval = setInterval(() => {
      this.nextSlide();
    }, 5000);
  }

  stopAutoPlay() {
    if (this.autoPlayInterval) {
      clearInterval(this.autoPlayInterval);
      this.autoPlayInterval = null;
    }
  }

  setupHoverPause() {
    this.carousel.addEventListener('mouseenter', () => this.stopAutoPlay());
    this.carousel.addEventListener('mouseleave', () => this.startAutoPlay());
  }
}

// ===== FORM HANDLER =====
class FormHandler {
  constructor() {
    this.contactForm = document.querySelector('.contact-form');
    this.init();
  }

  init() {
    if (!this.contactForm) return;

    this.setupFormValidation();
    this.setupFormSubmission();
  }

  setupFormValidation() {
    const inputs = this.contactForm.querySelectorAll('input, select, textarea');

    inputs.forEach(input => {
      input.addEventListener('blur', () => this.validateField(input));
      input.addEventListener('input', () => this.clearErrors(input));
    });
  }

  validateField(field) {
    const value = field.value.trim();
    const fieldName = field.getAttribute('placeholder') || field.tagName.toLowerCase();

    this.clearErrors(field);

    if (field.hasAttribute('required') && !value) {
      this.showError(field, `${fieldName}是必填项`);
      return false;
    }

    if (field.type === 'email' && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        this.showError(field, '请输入有效的邮箱地址');
        return false;
      }
    }

    return true;
  }

  showError(field, message) {
    field.style.borderColor = 'var(--error-color)';

    let errorElement = field.parentNode.querySelector('.error-message');
    if (!errorElement) {
      errorElement = document.createElement('div');
      errorElement.className = 'error-message';
      errorElement.style.cssText = `
        color: var(--error-color);
        font-size: var(--font-size-sm);
        margin-top: var(--spacing-1);
      `;
      field.parentNode.appendChild(errorElement);
    }

    errorElement.textContent = message;
  }

  clearErrors(field) {
    field.style.borderColor = 'var(--gray-200)';
    const errorElement = field.parentNode.querySelector('.error-message');
    if (errorElement) {
      errorElement.remove();
    }
  }

  setupFormSubmission() {
    this.contactForm.addEventListener('submit', async (e) => {
      e.preventDefault();

      const formData = new FormData(this.contactForm);
      const inputs = this.contactForm.querySelectorAll('input, select, textarea');
      let isValid = true;

      inputs.forEach(input => {
        if (!this.validateField(input)) {
          isValid = false;
        }
      });

      if (!isValid) return;

      const submitBtn = this.contactForm.querySelector('.btn');
      const originalText = submitBtn.innerHTML;

      submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发送中...';
      submitBtn.disabled = true;

      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 2000));

        this.showSuccessMessage();
        this.contactForm.reset();
      } catch (error) {
        this.showErrorMessage('发送失败，请稍后重试');
      } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
      }
    });
  }

  showSuccessMessage() {
    const message = document.createElement('div');
    message.className = 'success-message';
    message.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: var(--success-color);
      color: white;
      padding: var(--spacing-6);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-xl);
      z-index: 9999;
      text-align: center;
    `;
    message.innerHTML = `
      <i class="fas fa-check-circle" style="font-size: var(--font-size-2xl); margin-bottom: var(--spacing-2);"></i>
      <p>消息发送成功！我们会尽快与您联系。</p>
    `;

    document.body.appendChild(message);

    setTimeout(() => {
      message.style.opacity = '0';
      setTimeout(() => message.remove(), 300);
    }, 3000);
  }

  showErrorMessage(text) {
    const message = document.createElement('div');
    message.className = 'error-message';
    message.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: var(--error-color);
      color: white;
      padding: var(--spacing-6);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-xl);
      z-index: 9999;
      text-align: center;
    `;
    message.innerHTML = `
      <i class="fas fa-exclamation-circle" style="font-size: var(--font-size-2xl); margin-bottom: var(--spacing-2);"></i>
      <p>${text}</p>
    `;

    document.body.appendChild(message);

    setTimeout(() => {
      message.style.opacity = '0';
      setTimeout(() => message.remove(), 300);
    }, 3000);
  }
}

// ===== BACK TO TOP =====
class BackToTop {
  constructor() {
    this.button = document.getElementById('back-to-top');
    this.init();
  }

  init() {
    if (!this.button) return;

    this.setupScrollVisibility();
    this.setupClickHandler();
  }

  setupScrollVisibility() {
    const handleScroll = throttle(() => {
      const scrolled = window.scrollY > 300;
      this.button.classList.toggle('visible', scrolled);
    }, 100);

    window.addEventListener('scroll', handleScroll);
  }

  setupClickHandler() {
    this.button.addEventListener('click', () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });
  }
}

// ===== PERFORMANCE OPTIMIZATIONS =====
class PerformanceOptimizer {
  constructor() {
    this.init();
  }

  init() {
    this.setupLazyLoading();
    this.setupImageOptimization();
    this.preloadCriticalResources();
  }

  setupLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');

    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.getAttribute('data-src');
          img.removeAttribute('data-src');
          imageObserver.unobserve(img);
        }
      });
    });

    images.forEach(img => imageObserver.observe(img));
  }

  setupImageOptimization() {
    const images = document.querySelectorAll('img');

    images.forEach(img => {
      img.addEventListener('load', () => {
        img.style.opacity = '1';
      });

      img.style.opacity = '0';
      img.style.transition = 'opacity 0.3s ease';
    });
  }

  preloadCriticalResources() {
    const criticalResources = [
      'assets/css/main.css',
      'assets/js/main.js'
    ];

    criticalResources.forEach(resource => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = resource;
      link.as = resource.endsWith('.css') ? 'style' : 'script';
      document.head.appendChild(link);
    });
  }
}

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
  // Initialize all components
  const loadingScreen = new LoadingScreen();
  const cursorFollower = new CursorFollower();
  const navigation = new Navigation();
  const scrollAnimations = new ScrollAnimations();
  const productCarousel = new ProductCarousel();
  const formHandler = new FormHandler();
  const backToTop = new BackToTop();
  const performanceOptimizer = new PerformanceOptimizer();

  // Initialize particle system after loading
  setTimeout(() => {
    const canvas = document.getElementById('particle-canvas');
    if (canvas) {
      particleSystem = new ParticleSystem(canvas);
    }
  }, 1000);

  // Add keyboard navigation support
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      const navMenu = document.getElementById('nav-menu');
      const navToggle = document.getElementById('nav-toggle');
      navMenu.classList.remove('active');
      navToggle.classList.remove('active');
    }
  });

  // Add focus management for accessibility
  const focusableElements = document.querySelectorAll(
    'a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );

  focusableElements.forEach(el => {
    el.addEventListener('focus', () => {
      el.style.outline = '2px solid var(--primary-color)';
      el.style.outlineOffset = '2px';
    });

    el.addEventListener('blur', () => {
      el.style.outline = 'none';
    });
  });
});

// ===== WINDOW EVENTS =====
window.addEventListener('load', () => {
  // Optimize performance after full load
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      // Perform non-critical tasks
      console.log('云深处科技官网加载完成');
    });
  }
});

window.addEventListener('beforeunload', () => {
  // Cleanup
  if (particleSystem) {
    particleSystem = null;
  }
});
